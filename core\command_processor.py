"""Command processing module for JARVIS AI Assistant."""

import re
from typing import Dict, List, Tuple, Any, Optional
from datetime import datetime

from config.logging_config import LoggerMixin
from utilities.exceptions import JARVISException

class CommandProcessor(LoggerMixin):
    """Process and categorize user commands for JARVIS."""

    def __init__(self):
        """Initialize command processor."""
        self.command_patterns = self._initialize_command_patterns()
        self.logger.info("Command processor initialized")

    def _initialize_command_patterns(self) -> Dict[str, List[str]]:
        """
        Initialize command patterns for different categories.

        Returns:
            Dictionary mapping command categories to regex patterns
        """
        return {
            # System control commands
            "system_control": [
                r"(open|launch|start|run)\s+(.+)",
                r"(close|quit|exit|kill)\s+(.+)",
                r"(volume|brightness)\s+(up|down|set)\s*(\d+)?",
                r"(shutdown|restart|reboot|sleep)\s*(computer|system)?",
                r"(lock|unlock)\s*(screen|computer)?",
                r"(wifi|bluetooth)\s+(on|off|enable|disable)",
            ],

            # File management commands
            "file_management": [
                r"(create|make|new)\s+(file|folder|directory)\s+(.+)",
                r"(delete|remove|rm)\s+(file|folder)?\s*(.+)",
                r"(copy|move|rename)\s+(.+)\s+(to|as)\s+(.+)",
                r"(find|search|locate)\s+(file|folder)?\s*(.+)",
                r"(list|show)\s+(files|folders|contents)\s*(in|of)?\s*(.+)?",
                r"(open|edit)\s+(file|folder)?\s*(.+)",
            ],

            # Web and information commands
            "web_information": [
                r"(search|google|find)\s+(for\s+)?(.+)",
                r"(weather|temperature)\s*(in|for)?\s*(.+)?",
                r"(news|headlines)\s*(about|on)?\s*(.+)?",
                r"(what|who|when|where|why|how)\s+(.+)",
                r"(tell me|show me)\s+(about\s+)?(.+)",
                r"(open|go to|visit)\s+(website|url|site)?\s*(.+)",
            ],

            # Memory and notes commands
            "memory_notes": [
                r"(remember|note|save)\s+(.+)",
                r"(remind me|reminder)\s+(to\s+)?(.+)\s+(at|in|on)\s+(.+)",
                r"(what did i|show me|list)\s+(notes|reminders|memories)",
                r"(forget|delete|remove)\s+(note|reminder|memory)\s*(.+)?",
            ],

            # Voice and interaction commands
            "voice_interaction": [
                r"(speak|say|tell me)\s+(.+)",
                r"(stop|pause|quiet|silence)",
                r"(louder|quieter|volume)\s*(up|down)?",
                r"(repeat|say again)",
                r"(listen|start listening)",
            ],

            # Time and scheduling commands
            "time_scheduling": [
                r"^(what time|time)\s*(is it)?$",
                r"^(what date|date)\s*(is it|today)?$",
                r"(set|create)\s+(timer|alarm)\s+(for\s+)?(.+)",
                r"(schedule|plan)\s+(.+)\s+(for|at|on)\s+(.+)",
                r"(calendar|appointments|meetings)\s*(today|tomorrow|this week)?",
            ],

            # General conversation
            "conversation": [
                r"(hello|hi|hey|good morning|good afternoon|good evening)",
                r"(how are you|how's it going|what's up)",
                r"(thank you|thanks|appreciate it)",
                r"(goodbye|bye|see you|good night)",
                r"(help|what can you do|commands|capabilities)",
                r"(joke|tell me a joke|something funny)",
            ]
        }

    def process_command(self, user_input: str) -> Dict[str, Any]:
        """
        Process user input and determine command category and parameters.

        Args:
            user_input: Raw user input text

        Returns:
            Dictionary containing command information
        """
        if not user_input or not user_input.strip():
            return self._create_command_result("unknown", user_input, {})

        # Clean and normalize input
        cleaned_input = self._clean_input(user_input)

        # Try to match command patterns
        for category, patterns in self.command_patterns.items():
            for pattern in patterns:
                match = re.search(pattern, cleaned_input, re.IGNORECASE)
                if match:
                    parameters = self._extract_parameters(category, pattern, match)
                    return self._create_command_result(category, cleaned_input, parameters, match.groups())

        # If no pattern matches, treat as general conversation or AI query
        return self._create_command_result("ai_query", cleaned_input, {})

    def _clean_input(self, user_input: str) -> str:
        """
        Clean and normalize user input.

        Args:
            user_input: Raw user input

        Returns:
            Cleaned input string
        """
        # Remove extra whitespace
        cleaned = ' '.join(user_input.split())

        # Remove common filler words at the beginning
        filler_words = ["please", "can you", "could you", "would you", "i need", "i want", "i'd like"]
        cleaned_lower = cleaned.lower()

        for filler in filler_words:
            if cleaned_lower.startswith(filler + " "):
                cleaned = cleaned[len(filler):].strip()
                break

        return cleaned

    def _extract_parameters(self, category: str, pattern: str, match: re.Match) -> Dict[str, Any]:
        """
        Extract parameters from regex match based on command category.

        Args:
            category: Command category
            pattern: Regex pattern that matched
            match: Regex match object

        Returns:
            Dictionary of extracted parameters
        """
        groups = match.groups()
        parameters = {}

        if category == "system_control":
            if "open|launch|start|run" in pattern:
                parameters = {"action": groups[0], "target": groups[1]}
            elif "close|quit|exit|kill" in pattern:
                parameters = {"action": groups[0], "target": groups[1]}
            elif "volume|brightness" in pattern:
                parameters = {
                    "setting": groups[0],
                    "action": groups[1],
                    "value": groups[2] if len(groups) > 2 and groups[2] else None
                }
            elif "shutdown|restart|reboot|sleep" in pattern:
                parameters = {"action": groups[0]}
            elif "wifi|bluetooth" in pattern:
                parameters = {"device": groups[0], "action": groups[1]}

        elif category == "file_management":
            if "create|make|new" in pattern:
                parameters = {"action": groups[0], "type": groups[1], "name": groups[2]}
            elif "delete|remove|rm" in pattern:
                parameters = {"action": groups[0], "target": groups[2] if len(groups) > 2 else groups[1]}
            elif "copy|move|rename" in pattern:
                parameters = {"action": groups[0], "source": groups[1], "destination": groups[3]}
            elif "find|search|locate" in pattern:
                parameters = {"action": groups[0], "target": groups[2] if len(groups) > 2 else groups[1]}
            elif "list|show" in pattern:
                parameters = {"action": groups[0], "target": groups[1], "location": groups[3] if len(groups) > 3 else None}

        elif category == "web_information":
            if "search|google|find" in pattern:
                parameters = {"action": "search", "query": groups[2] if len(groups) > 2 else groups[1]}
            elif "weather|temperature" in pattern:
                parameters = {"action": "weather", "location": groups[2] if len(groups) > 2 and groups[2] else None}
            elif "news|headlines" in pattern:
                parameters = {"action": "news", "topic": groups[2] if len(groups) > 2 and groups[2] else None}
            elif "what|who|when|where|why|how" in pattern:
                parameters = {"action": "question", "question": groups[1]}
            elif "open|go to|visit" in pattern:
                parameters = {"action": "open_url", "url": groups[2] if len(groups) > 2 else groups[1]}

        elif category == "memory_notes":
            if "remember|note|save" in pattern:
                parameters = {"action": "save", "content": groups[1]}
            elif "remind me|reminder" in pattern:
                parameters = {"action": "reminder", "task": groups[2], "time": groups[4]}
            elif "what did i|show me|list" in pattern:
                parameters = {"action": "list", "type": groups[1]}

        elif category == "time_scheduling":
            if "what time|time" in pattern:
                parameters = {"action": "get_time"}
            elif "what date|date" in pattern:
                parameters = {"action": "get_date"}
            elif "set|create" in pattern and "timer|alarm" in pattern:
                parameters = {"action": "set_timer", "type": groups[1], "duration": groups[3]}

        return parameters

    def _create_command_result(
        self,
        category: str,
        original_input: str,
        parameters: Dict[str, Any],
        groups: Tuple = None
    ) -> Dict[str, Any]:
        """
        Create a standardized command result dictionary.

        Args:
            category: Command category
            original_input: Original user input
            parameters: Extracted parameters
            groups: Regex groups (optional)

        Returns:
            Command result dictionary
        """
        return {
            "category": category,
            "original_input": original_input,
            "parameters": parameters,
            "groups": groups,
            "timestamp": datetime.now().isoformat(),
            "confidence": self._calculate_confidence(category, parameters)
        }

    def _calculate_confidence(self, category: str, parameters: Dict[str, Any]) -> float:
        """
        Calculate confidence score for command classification.

        Args:
            category: Command category
            parameters: Extracted parameters

        Returns:
            Confidence score (0.0 to 1.0)
        """
        # Base confidence based on category
        base_confidence = {
            "system_control": 0.9,
            "file_management": 0.85,
            "web_information": 0.8,
            "memory_notes": 0.85,
            "voice_interaction": 0.9,
            "time_scheduling": 0.9,
            "conversation": 0.7,
            "ai_query": 0.6,
            "unknown": 0.1
        }

        confidence = base_confidence.get(category, 0.5)

        # Adjust based on parameter completeness
        if parameters:
            required_params = self._get_required_parameters(category)
            if required_params:
                param_completeness = len([p for p in required_params if p in parameters and parameters[p]]) / len(required_params)
                confidence *= (0.5 + 0.5 * param_completeness)

        return min(1.0, confidence)

    def _get_required_parameters(self, category: str) -> List[str]:
        """
        Get required parameters for a command category.

        Args:
            category: Command category

        Returns:
            List of required parameter names
        """
        required_params = {
            "system_control": ["action"],
            "file_management": ["action"],
            "web_information": ["action"],
            "memory_notes": ["action"],
            "voice_interaction": [],
            "time_scheduling": ["action"],
            "conversation": [],
            "ai_query": []
        }

        return required_params.get(category, [])

    def get_command_suggestions(self, partial_input: str) -> List[str]:
        """
        Get command suggestions based on partial input.

        Args:
            partial_input: Partial user input

        Returns:
            List of suggested commands
        """
        suggestions = []
        partial_lower = partial_input.lower()

        # Common command starters
        command_starters = {
            "open": ["open notepad", "open calculator", "open browser"],
            "search": ["search for", "search google for"],
            "what": ["what time is it", "what's the weather", "what date is it"],
            "create": ["create file", "create folder", "create reminder"],
            "tell": ["tell me about", "tell me a joke", "tell me the time"],
            "show": ["show me files", "show me weather", "show me news"],
            "set": ["set timer", "set reminder", "set volume"],
            "play": ["play music", "play video", "play sound"]
        }

        for starter, examples in command_starters.items():
            if starter.startswith(partial_lower) or partial_lower.startswith(starter):
                suggestions.extend(examples)

        return suggestions[:5]  # Return top 5 suggestions
