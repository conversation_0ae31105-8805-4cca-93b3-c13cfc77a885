# JARVIS AI Assistant

A comprehensive AI assistant built in Python with modular architecture, featuring voice interaction, system control, and intelligent conversation capabilities powered by OpenRouter's ChatGPT-4o API.

## 🌟 Features

### Core Capabilities
- **AI-Powered Conversations**: Intelligent responses using OpenRouter ChatGPT-4o
- **Voice Interaction**: Speech recognition and text-to-speech capabilities
- **Wake Word Detection**: Activate with "Jarvis" wake word
- **System Control**: Launch/close applications, manage files, control settings
- **Web Tasks**: Search, weather, news, and information gathering
- **Memory System**: Conversation history and user preferences
- **Security**: Built-in safety checks and command validation

### Voice Features
- Continuous voice listening with wake word detection
- Natural language understanding
- High-quality text-to-speech output
- Microphone calibration and noise adjustment
- Voice command processing

### System Control
- Application management (launch, close, monitor)
- File system operations (create, delete, copy, move)
- System settings control (volume, brightness, network)
- Process monitoring and management
- Safe command execution with validation

### Personality
- Calm, respectful, and witty personality
- Configurable response styles
- Context-aware conversations
- Learning from user interactions

## 🚀 Quick Start

### Prerequisites
- Python 3.8+
- Windows 10/11 (for full system control)
- Microphone and speakers (for voice features)
- Internet connection

### Installation

1. **Clone the repository**
```bash
git clone <repository-url>
cd jarvis_ai_assistant
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Run setup**
```bash
python main.py --setup
```

4. **Test the system**
```bash
python main.py --test
```

5. **Start JARVIS**
```bash
python main.py
```

## 🎯 Usage Examples

### Voice Commands
```
"Jarvis, what time is it?"
"Jarvis, open notepad"
"Jarvis, search for Python tutorials"
"Jarvis, what's the weather?"
```

### Text Commands
```
what time is it
open calculator
search for artificial intelligence
weather
news
create file test.txt
list files
```

### System Control
```
open notepad
close calculator
volume up
shutdown computer
list running processes
```

## 🏗️ Architecture

### Modular Design
```
jarvis_ai_assistant/
├── core/                 # Core AI engine and command processing
├── voice/                # Speech recognition and TTS
├── system_control/       # System and application management
├── web_tasks/           # Web search, weather, news
├── memory/              # Data storage and user preferences
├── utilities/           # Security, helpers, exceptions
├── config/              # Settings and logging
└── data/                # Local data storage
```

### Key Components

- **AI Engine**: OpenRouter ChatGPT-4o integration
- **Command Processor**: Natural language command parsing
- **Voice System**: Speech recognition and synthesis
- **Security Validator**: Safe command execution
- **Memory System**: Persistent data storage

## ⚙️ Configuration

### Environment Variables (.env)
```bash
# AI Configuration
OPENROUTER_API_KEY=your_api_key_here
OPENROUTER_MODEL=openai/gpt-4o

# Voice Settings
VOICE_ENABLED=true
WAKE_WORD=jarvis
VOICE_RATE=200
VOICE_VOLUME=0.9

# Security
SAFE_MODE=true
ALLOW_SYSTEM_COMMANDS=true

# Personality
PERSONALITY_MODE=witty
RESPONSE_STYLE=calm_respectful
```

### Personality Modes
- **witty**: Clever and humorous responses
- **professional**: Formal and efficient
- **friendly**: Warm and approachable

## 🔧 Command Line Options

```bash
python main.py                # Start JARVIS normally
python main.py --no-voice     # Disable voice features
python main.py --test         # Run component tests
python main.py --setup        # Run initial setup
```

## 🛡️ Security Features

- Command validation before execution
- Safe mode with restricted operations
- File path security checks
- User confirmation for destructive operations
- Protected system directories

## 📚 API Reference

### Core Classes

#### JARVIS
Main assistant class that orchestrates all components.

#### AIEngine
Handles communication with OpenRouter ChatGPT-4o API.

#### CommandProcessor
Processes and categorizes user commands.

#### SpeechRecognizer
Manages voice input and speech recognition.

#### TextToSpeech
Handles voice output and speech synthesis.

## 🔍 Troubleshooting

### Common Issues

1. **Voice not working**: Check microphone permissions and volume
2. **API errors**: Verify OpenRouter API key and credits
3. **Import errors**: Install dependencies with `pip install -r requirements.txt`
4. **Permission denied**: Run as administrator for system operations

### Debug Mode
Enable detailed logging by setting `LOG_LEVEL=DEBUG` in `.env`.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenRouter for AI API access
- SpeechRecognition library for voice input
- pyttsx3 for text-to-speech
- All open-source contributors

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the documentation in `/docs`
3. Create an issue on GitHub

---

**JARVIS AI Assistant** - Your intelligent computing companion 🤖✨
