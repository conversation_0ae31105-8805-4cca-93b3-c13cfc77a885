"""Speech recognition module for JARVIS AI Assistant."""

import speech_recognition as sr
import threading
import time
from typing import Optional, Callable
from queue import Queue, Empty

from config.settings import settings
from config.logging_config import LoggerMixin
from utilities.exceptions import VoiceException

class SpeechRecognizer(LoggerMixin):
    """Speech recognition handler for voice input."""

    def __init__(self, callback: Optional[Callable[[str], None]] = None):
        """
        Initialize speech recognizer.

        Args:
            callback: Function to call when speech is recognized
        """
        self.recognizer = sr.Recognizer()
        self.microphone = None
        self.callback = callback
        self.is_listening = False
        self.listen_thread = None
        self.audio_queue = Queue()

        # Recognition settings
        self.energy_threshold = 300  # Lower initial threshold
        self.dynamic_energy_threshold = True
        self.pause_threshold = 0.8
        self.phrase_threshold = 0.3
        self.timeout_duration = 1  # Shorter timeout for responsiveness

        self._initialize_microphone()
        self.logger.info("Speech recognizer initialized")

    def _initialize_microphone(self):
        """Initialize and calibrate the microphone."""
        try:
            # Try to initialize microphone with default device
            self.microphone = sr.Microphone()

            # Calibrate for ambient noise with shorter duration
            with self.microphone as source:
                self.logger.info("Calibrating microphone for ambient noise...")
                self.recognizer.adjust_for_ambient_noise(source, duration=1)

            # Configure recognizer settings with adaptive thresholds
            if self.recognizer.energy_threshold < 100:
                # If threshold is too low, set a minimum
                self.recognizer.energy_threshold = max(self.recognizer.energy_threshold, 100)

            self.recognizer.dynamic_energy_threshold = self.dynamic_energy_threshold
            self.recognizer.pause_threshold = self.pause_threshold
            self.recognizer.phrase_threshold = self.phrase_threshold

            self.logger.info(f"Microphone calibrated. Energy threshold: {self.recognizer.energy_threshold}")

        except Exception as e:
            error_msg = f"Failed to initialize microphone: {str(e)}"
            self.logger.error(error_msg)
            # Don't raise exception, allow system to continue with limited functionality
            self.logger.warning("Continuing without microphone - voice input will not work")
            self.microphone = None

    def start_listening(self):
        """Start continuous listening for speech."""
        if self.is_listening:
            self.logger.warning("Already listening for speech")
            return

        if not self.microphone:
            raise VoiceException("Microphone not initialized")

        self.is_listening = True
        self.listen_thread = threading.Thread(target=self._listen_continuously, daemon=True)
        self.listen_thread.start()

        self.logger.info("Started continuous speech listening")

    def stop_listening(self):
        """Stop continuous listening."""
        self.is_listening = False
        if self.listen_thread and self.listen_thread.is_alive():
            self.listen_thread.join(timeout=2)

        self.logger.info("Stopped speech listening")

    def _listen_continuously(self):
        """Continuously listen for speech in a separate thread."""
        while self.is_listening:
            try:
                with self.microphone as source:
                    # Listen for audio with shorter timeout for better responsiveness
                    audio = self.recognizer.listen(source, timeout=self.timeout_duration, phrase_time_limit=5)

                # Process audio immediately instead of queuing
                self._process_audio_immediately(audio)

            except sr.WaitTimeoutError:
                # Timeout is normal, continue listening
                continue
            except Exception as e:
                self.logger.error(f"Error during continuous listening: {str(e)}")
                time.sleep(0.1)  # Brief pause before retrying

        # Process any remaining audio in queue
        self._process_audio_queue()

    def _process_audio_immediately(self, audio):
        """
        Process audio immediately for better responsiveness.

        Args:
            audio: Audio data from speech_recognition
        """
        try:
            text = self._recognize_audio(audio)
            if text and self.callback:
                self.callback(text)
        except Exception as e:
            self.logger.error(f"Error processing audio immediately: {str(e)}")

    def _process_audio_queue(self):
        """Process audio from the queue."""
        while not self.audio_queue.empty():
            try:
                audio = self.audio_queue.get_nowait()
                text = self._recognize_audio(audio)
                if text and self.callback:
                    self.callback(text)
            except Empty:
                break
            except Exception as e:
                self.logger.error(f"Error processing audio queue: {str(e)}")

    def _recognize_audio(self, audio) -> Optional[str]:
        """
        Recognize speech from audio data.

        Args:
            audio: Audio data from speech_recognition

        Returns:
            Recognized text or None if recognition failed
        """
        try:
            # Use Google Speech Recognition (free tier)
            text = self.recognizer.recognize_google(audio)
            self.logger.debug(f"Recognized speech: {text}")
            return text.strip()

        except sr.UnknownValueError:
            self.logger.debug("Could not understand audio")
            return None
        except sr.RequestError as e:
            self.logger.error(f"Speech recognition service error: {str(e)}")
            return None
        except Exception as e:
            self.logger.error(f"Unexpected error during speech recognition: {str(e)}")
            return None

    def recognize_once(self, timeout: float = 5.0) -> Optional[str]:
        """
        Listen for and recognize a single phrase.

        Args:
            timeout: Maximum time to wait for speech

        Returns:
            Recognized text or None if no speech detected
        """
        if not self.microphone:
            raise VoiceException("Microphone not initialized")

        try:
            with self.microphone as source:
                self.logger.debug("Listening for single phrase...")
                audio = self.recognizer.listen(source, timeout=timeout, phrase_time_limit=10)

            return self._recognize_audio(audio)

        except sr.WaitTimeoutError:
            self.logger.debug("No speech detected within timeout")
            return None
        except Exception as e:
            error_msg = f"Error during single phrase recognition: {str(e)}"
            self.logger.error(error_msg)
            raise VoiceException(error_msg)

    def set_callback(self, callback: Callable[[str], None]):
        """
        Set the callback function for recognized speech.

        Args:
            callback: Function to call with recognized text
        """
        self.callback = callback
        self.logger.debug("Speech recognition callback updated")

    def adjust_for_noise(self, duration: float = 1.0):
        """
        Adjust microphone sensitivity for ambient noise.

        Args:
            duration: Duration to sample ambient noise
        """
        if not self.microphone:
            raise VoiceException("Microphone not initialized")

        try:
            with self.microphone as source:
                self.logger.info(f"Adjusting for ambient noise ({duration}s)...")
                self.recognizer.adjust_for_ambient_noise(source, duration=duration)

            self.logger.info(f"Noise adjustment complete. New threshold: {self.recognizer.energy_threshold}")

        except Exception as e:
            error_msg = f"Failed to adjust for ambient noise: {str(e)}"
            self.logger.error(error_msg)
            raise VoiceException(error_msg)

    def get_microphone_info(self) -> dict:
        """Get information about available microphones."""
        try:
            mic_list = sr.Microphone.list_microphone_names()
            return {
                "available_microphones": mic_list,
                "current_device_index": getattr(self.microphone, 'device_index', None),
                "energy_threshold": self.recognizer.energy_threshold,
                "dynamic_energy_threshold": self.recognizer.dynamic_energy_threshold
            }
        except Exception as e:
            self.logger.error(f"Error getting microphone info: {str(e)}")
            return {"error": str(e)}

    def test_microphone(self) -> bool:
        """
        Test microphone functionality.

        Returns:
            True if microphone is working
        """
        try:
            self.logger.info("Testing microphone... Please say something.")
            result = self.recognize_once(timeout=5.0)

            if result:
                self.logger.info(f"Microphone test successful. Heard: '{result}'")
                return True
            else:
                self.logger.warning("Microphone test failed - no speech detected")
                return False

        except Exception as e:
            self.logger.error(f"Microphone test failed: {str(e)}")
            return False
