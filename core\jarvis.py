"""Main JARVIS AI Assistant class."""

import asyncio
import threading
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from config.settings import settings
from config.logging_config import LoggerMixin, setup_logging
from utilities.exceptions import JARVISException, ConfigurationException

# Import core modules
from .ai_engine import AIEngine
from .command_processor import CommandProcessor

# Import voice modules
from voice.speech_recognition import Speech<PERSON><PERSON>ognizer
from voice.text_to_speech import TextToSpeech
from voice.wake_word import WakeWordDetector

# Import system control modules
from system_control.app_manager import AppManager
from system_control.file_manager import FileManager
from system_control.system_settings import SystemSettings
from system_control.process_manager import ProcessManager

# Import web task modules
from web_tasks.web_search import WebSearch
from web_tasks.weather import WeatherService
from web_tasks.news import NewsService
from web_tasks.information import InformationService

# Import memory modules
from memory.storage import Storage
from memory.user_preferences import UserPreferences
from memory.conversation_history import ConversationHistory

# Import utilities
from utilities.security import SecurityValidator

class JARVIS(LoggerMixin):
    """Main JARVIS AI Assistant class."""

    def __init__(self):
        """Initialize JARVIS AI Assistant."""
        # Set up logging
        setup_logging(
            log_level=settings.LOG_LEVEL,
            log_file=settings.LOG_FILE,
            console_output=True
        )

        self.logger.info("Initializing JARVIS AI Assistant...")

        # Validate configuration
        if not settings.validate_required_settings():
            raise ConfigurationException("Required settings are missing. Please check your configuration.")

        # Initialize core components
        self.ai_engine = None
        self.command_processor = None
        self.security_validator = None

        # Initialize voice components
        self.speech_recognizer = None
        self.text_to_speech = None
        self.wake_word_detector = None

        # Initialize system control components
        self.app_manager = None
        self.file_manager = None
        self.system_settings = None
        self.process_manager = None

        # Initialize web task components
        self.web_search = None
        self.weather_service = None
        self.news_service = None
        self.information_service = None

        # Initialize memory components
        self.storage = None
        self.user_preferences = None
        self.conversation_history = None

        # State management
        self.is_running = False
        self.is_listening = False
        self.voice_enabled = settings.VOICE_ENABLED

        # Initialize all components
        self._initialize_components()

        self.logger.info("JARVIS AI Assistant initialized successfully")

    def _initialize_components(self):
        """Initialize all JARVIS components."""
        try:
            # Core components
            self.logger.info("Initializing core components...")
            self.ai_engine = AIEngine()
            self.command_processor = CommandProcessor()
            self.security_validator = SecurityValidator(safe_mode=settings.SAFE_MODE)

            # Voice components (if enabled)
            if self.voice_enabled:
                self.logger.info("Initializing voice components...")
                self.text_to_speech = TextToSpeech()
                self.speech_recognizer = SpeechRecognizer(callback=self._handle_voice_input)
                self.wake_word_detector = WakeWordDetector(
                    wake_word=settings.WAKE_WORD,
                    callback=self._handle_wake_word
                )

            # System control components
            self.logger.info("Initializing system control components...")
            self.app_manager = AppManager()
            self.file_manager = FileManager()
            self.system_settings = SystemSettings()
            self.process_manager = ProcessManager()

            # Web task components
            self.logger.info("Initializing web task components...")
            self.web_search = WebSearch()
            self.weather_service = WeatherService()
            self.news_service = NewsService()
            self.information_service = InformationService()

            # Memory components
            self.logger.info("Initializing memory components...")
            self.storage = Storage()
            self.user_preferences = UserPreferences()
            self.conversation_history = ConversationHistory()

            self.logger.info("All components initialized successfully")

        except Exception as e:
            error_msg = f"Failed to initialize JARVIS components: {str(e)}"
            self.logger.error(error_msg)
            raise JARVISException(error_msg)

    def start(self):
        """Start JARVIS AI Assistant."""
        if self.is_running:
            self.logger.warning("JARVIS is already running")
            return

        try:
            self.is_running = True
            self.logger.info("Starting JARVIS AI Assistant...")

            # Test AI engine connection
            if not self.ai_engine.test_connection():
                raise JARVISException("Failed to connect to AI engine")

            # Start voice components if enabled
            if self.voice_enabled:
                self._start_voice_system()

            # Load user preferences and conversation history
            self.user_preferences.load()
            self.conversation_history.load()

            # Send startup message
            startup_message = self._get_startup_message()
            self._respond(startup_message)

            self.logger.info("JARVIS AI Assistant started successfully")

        except Exception as e:
            self.is_running = False
            error_msg = f"Failed to start JARVIS: {str(e)}"
            self.logger.error(error_msg)
            raise JARVISException(error_msg)

    def stop(self):
        """Stop JARVIS AI Assistant."""
        if not self.is_running:
            return

        try:
            self.logger.info("Stopping JARVIS AI Assistant...")
            self.is_running = False

            # Stop voice components
            if self.voice_enabled:
                self._stop_voice_system()

            # Save data
            self.user_preferences.save()
            self.conversation_history.save()

            # Send shutdown message
            shutdown_message = "JARVIS is shutting down. Goodbye!"
            self._respond(shutdown_message)

            self.logger.info("JARVIS AI Assistant stopped")

        except Exception as e:
            self.logger.error(f"Error during JARVIS shutdown: {str(e)}")

    def _start_voice_system(self):
        """Start the voice recognition and wake word detection system."""
        if not self.voice_enabled:
            return

        try:
            # Test voice components
            if not self.text_to_speech.test_speech():
                self.logger.warning("Text-to-speech test failed")

            if not self.speech_recognizer.test_microphone():
                self.logger.warning("Microphone test failed")

            # Start wake word detection
            self.wake_word_detector.start_continuous_detection(self.speech_recognizer)
            self.is_listening = True

            self.logger.info("Voice system started successfully")

        except Exception as e:
            self.logger.error(f"Failed to start voice system: {str(e)}")
            self.voice_enabled = False

    def _stop_voice_system(self):
        """Stop the voice recognition system."""
        if not self.voice_enabled:
            return

        try:
            self.is_listening = False

            if self.wake_word_detector:
                self.wake_word_detector.stop_continuous_detection(self.speech_recognizer)

            if self.speech_recognizer:
                self.speech_recognizer.stop_listening()

            if self.text_to_speech:
                self.text_to_speech.shutdown()

            self.logger.info("Voice system stopped")

        except Exception as e:
            self.logger.error(f"Error stopping voice system: {str(e)}")

    def _handle_voice_input(self, text: str):
        """
        Handle voice input from speech recognition.

        Args:
            text: Recognized speech text
        """
        if not text or not self.is_running:
            return

        self.logger.info(f"Voice input received: {text}")
        self.process_input(text, input_type="voice")

    def _handle_wake_word(self, command: str):
        """
        Handle wake word detection.

        Args:
            command: Command after wake word
        """
        if not command or not self.is_running:
            return

        self.logger.info(f"Wake word detected with command: {command}")

        # Acknowledge wake word
        if self.voice_enabled:
            self.text_to_speech.speak("Yes?", priority=True)

        # Process the command
        self.process_input(command, input_type="voice")

    def process_input(self, user_input: str, input_type: str = "text") -> str:
        """
        Process user input and generate response.

        Args:
            user_input: User input text
            input_type: Type of input ("text" or "voice")

        Returns:
            Response text
        """
        if not user_input or not user_input.strip():
            return "I didn't catch that. Could you please repeat?"

        try:
            # Log the interaction
            self.logger.info(f"Processing {input_type} input: {user_input}")

            # Process command
            command_result = self.command_processor.process_command(user_input)

            # Execute command based on category
            response = self._execute_command(command_result)

            # Store conversation
            self.conversation_history.add_exchange(user_input, response)

            # Respond to user
            self._respond(response, input_type)

            return response

        except Exception as e:
            error_response = f"I encountered an error: {str(e)}"
            self.logger.error(f"Error processing input: {str(e)}")
            self._respond(error_response, input_type)
            return error_response

    def _execute_command(self, command_result: Dict[str, Any]) -> str:
        """
        Execute a command based on its category and parameters.

        Args:
            command_result: Command processing result

        Returns:
            Response text
        """
        category = command_result["category"]
        parameters = command_result["parameters"]
        original_input = command_result["original_input"]

        try:
            if category == "system_control":
                return self._handle_system_control(parameters)
            elif category == "file_management":
                return self._handle_file_management(parameters)
            elif category == "web_information":
                return self._handle_web_information(parameters)
            elif category == "memory_notes":
                return self._handle_memory_notes(parameters)
            elif category == "voice_interaction":
                return self._handle_voice_interaction(parameters)
            elif category == "time_scheduling":
                return self._handle_time_scheduling(parameters)
            elif category == "conversation":
                return self._handle_conversation(parameters, original_input)
            else:
                # Use AI engine for general queries
                return self.ai_engine.generate_response(original_input)

        except Exception as e:
            self.logger.error(f"Error executing {category} command: {str(e)}")
            return f"I encountered an error while executing that command: {str(e)}"

    def _respond(self, response: str, input_type: str = "text"):
        """
        Send response to user via appropriate channel.

        Args:
            response: Response text
            input_type: Original input type
        """
        # Always log the response
        self.logger.info(f"Response: {response}")

        # Print to console
        print(f"JARVIS: {response}")

        # Speak response if voice is enabled and input was voice
        if self.voice_enabled and input_type == "voice":
            self.text_to_speech.speak(response)

    def _get_startup_message(self) -> str:
        """Get startup message for JARVIS."""
        current_time = datetime.now().strftime("%H:%M")

        messages = [
            f"Good morning! JARVIS is online and ready to assist you. The time is {current_time}.",
            f"Hello! JARVIS AI Assistant is now active. Current time: {current_time}.",
            f"JARVIS reporting for duty! All systems operational. Time: {current_time}."
        ]

        # Use AI to generate a personalized startup message
        try:
            prompt = f"Generate a brief, friendly startup message for JARVIS AI assistant. Current time is {current_time}. Be witty and professional."
            return self.ai_engine.generate_response(prompt, include_history=False, max_tokens=100)
        except:
            return messages[0]  # Fallback to default message

    # Placeholder methods for command handlers (to be implemented)
    def _handle_system_control(self, parameters: Dict[str, Any]) -> str:
        """Handle system control commands."""
        action = parameters.get("action", "")
        target = parameters.get("target", "")

        if action in ["open", "launch", "start", "run"]:
            success, message = self.app_manager.launch_application(target)
            return message
        elif action in ["close", "quit", "exit", "kill"]:
            success, message = self.app_manager.close_application(target)
            return message
        elif action == "volume":
            volume_action = parameters.get("action", "")
            if volume_action == "up":
                return "Volume increased."
            elif volume_action == "down":
                return "Volume decreased."
            else:
                return "Volume control functionality will be implemented."
        else:
            return f"System control action '{action}' will be implemented."

    def _handle_file_management(self, parameters: Dict[str, Any]) -> str:
        """Handle file management commands."""
        return "File management functionality will be implemented."

    def _handle_web_information(self, parameters: Dict[str, Any]) -> str:
        """Handle web information commands."""
        action = parameters.get("action", "")

        if action == "search":
            query = parameters.get("query", "")
            return self.web_search.get_search_summary(query)
        elif action == "weather":
            location = parameters.get("location", "")
            return self.weather_service.get_weather(location)
        elif action == "news":
            topic = parameters.get("topic", "")
            return self.news_service.get_news(topic)
        else:
            return "Web information functionality will be implemented."

    def _handle_memory_notes(self, parameters: Dict[str, Any]) -> str:
        """Handle memory and notes commands."""
        return "Memory and notes functionality will be implemented."

    def _handle_voice_interaction(self, parameters: Dict[str, Any]) -> str:
        """Handle voice interaction commands."""
        return "Voice interaction functionality will be implemented."

    def _handle_time_scheduling(self, parameters: Dict[str, Any]) -> str:
        """Handle time and scheduling commands."""
        action = parameters.get("action", "")

        if action == "get_time":
            current_time = datetime.now().strftime("%I:%M %p")
            return f"The current time is {current_time}."
        elif action == "get_date":
            current_date = datetime.now().strftime("%A, %B %d, %Y")
            return f"Today is {current_date}."
        else:
            current_time = datetime.now().strftime("%I:%M %p")
            current_date = datetime.now().strftime("%A, %B %d, %Y")
            return f"The current time is {current_time} and today is {current_date}."

    def _handle_conversation(self, parameters: Dict[str, Any], original_input: str) -> str:
        """Handle general conversation."""
        return self.ai_engine.generate_response(original_input)
