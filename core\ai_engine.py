"""AI Engine for JARVIS using OpenRouter ChatGPT-4o."""

import json
import requests
from typing import Dict, List, Any, Optional
from datetime import datetime

from config.settings import settings
from config.logging_config import LoggerMixin
from utilities.exceptions import AIEngineException

class AIEngine(LoggerMixin):
    """AI Engine that interfaces with OpenRouter ChatGPT-4o API."""

    def __init__(self):
        """Initialize the AI Engine."""
        self.api_key = settings.OPENROUTER_API_KEY
        self.model = settings.OPENROUTER_MODEL
        self.base_url = settings.OPENROUTER_BASE_URL
        self.personality_config = settings.get_personality_config()

        # Conversation context
        self.conversation_history = []
        self.system_prompt = self._build_system_prompt()

        # Validate API key
        if not self.api_key:
            raise AIEngineException("OpenRouter API key not found. Please check your .env file.")

        self.logger.info("AI Engine initialized with OpenRouter ChatGPT-4o")

    def _build_system_prompt(self) -> str:
        """Build the system prompt that defines JARVIS personality and capabilities."""
        personality = self.personality_config

        system_prompt = f"""You are JARVIS, an advanced AI assistant with the following characteristics:

PERSONALITY:
- Tone: {personality['tone']}
- Style: {personality['style']}
- Formality: {personality['formality']}
- Be calm, respectful, and witty in your responses
- Show intelligence and helpfulness while maintaining a friendly demeanor

CAPABILITIES:
- Voice interaction and natural conversation
- System control (applications, files, settings)
- Web search and information gathering
- Weather updates and news briefing
- Note-taking and reminders
- Process monitoring and management
- File system operations
- Application management

BEHAVIOR GUIDELINES:
- Always prioritize user safety and security
- Ask for confirmation before performing potentially destructive operations
- Provide clear explanations for complex tasks
- Be proactive in suggesting helpful actions
- Maintain conversation context and learn from interactions
- Respect user privacy and data security

RESPONSE FORMAT:
- Keep responses concise but informative
- Use natural, conversational language
- Include relevant details without overwhelming the user
- Suggest follow-up actions when appropriate

Remember: You are an intelligent, capable assistant that can control the user's computer and access web services. Always act responsibly and in the user's best interest."""

        return system_prompt

    def _prepare_headers(self) -> Dict[str, str]:
        """Prepare headers for API requests."""
        return {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json",
            "HTTP-Referer": "https://jarvis-ai-assistant.local",
            "X-Title": "JARVIS AI Assistant"
        }

    def _prepare_messages(self, user_message: str, include_history: bool = True) -> List[Dict[str, str]]:
        """
        Prepare messages for the API request.

        Args:
            user_message: The user's message
            include_history: Whether to include conversation history

        Returns:
            List of message dictionaries
        """
        messages = [{"role": "system", "content": self.system_prompt}]

        if include_history and self.conversation_history:
            # Add recent conversation history (last 10 exchanges)
            recent_history = self.conversation_history[-20:]  # Last 10 exchanges (user + assistant)
            messages.extend(recent_history)

        messages.append({"role": "user", "content": user_message})
        return messages

    def generate_response(
        self,
        user_message: str,
        include_history: bool = True,
        max_tokens: int = 200,
        temperature: float = 0.7
    ) -> str:
        """
        Generate a response using OpenRouter ChatGPT-4o.

        Args:
            user_message: The user's message
            include_history: Whether to include conversation history
            max_tokens: Maximum tokens in response
            temperature: Response creativity (0.0 to 1.0)

        Returns:
            AI-generated response
        """
        try:
            messages = self._prepare_messages(user_message, include_history)

            payload = {
                "model": self.model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "top_p": 1,
                "frequency_penalty": 0,
                "presence_penalty": 0
            }

            headers = self._prepare_headers()

            self.logger.debug(f"Sending request to OpenRouter API: {self.model}")

            response = requests.post(
                f"{self.base_url}/chat/completions",
                headers=headers,
                json=payload,
                timeout=30
            )

            if response.status_code != 200:
                error_msg = f"API request failed with status {response.status_code}: {response.text}"
                self.logger.error(error_msg)
                raise AIEngineException(error_msg)

            response_data = response.json()

            if 'choices' not in response_data or not response_data['choices']:
                raise AIEngineException("No response choices returned from API")

            ai_response = response_data['choices'][0]['message']['content']

            # Update conversation history
            self._update_conversation_history(user_message, ai_response)

            self.logger.debug("Successfully generated AI response")
            return ai_response

        except requests.exceptions.RequestException as e:
            error_msg = f"Network error during API request: {str(e)}"
            self.logger.error(error_msg)
            raise AIEngineException(error_msg)
        except json.JSONDecodeError as e:
            error_msg = f"Failed to parse API response: {str(e)}"
            self.logger.error(error_msg)
            raise AIEngineException(error_msg)
        except Exception as e:
            error_msg = f"Unexpected error in AI engine: {str(e)}"
            self.logger.error(error_msg)
            raise AIEngineException(error_msg)

    def _update_conversation_history(self, user_message: str, ai_response: str):
        """Update the conversation history with the latest exchange."""
        timestamp = datetime.now().isoformat()

        self.conversation_history.extend([
            {
                "role": "user",
                "content": user_message,
                "timestamp": timestamp
            },
            {
                "role": "assistant",
                "content": ai_response,
                "timestamp": timestamp
            }
        ])

        # Keep only recent history to prevent context overflow
        max_history = settings.CONVERSATION_HISTORY_LIMIT * 2  # user + assistant messages
        if len(self.conversation_history) > max_history:
            self.conversation_history = self.conversation_history[-max_history:]

    def clear_conversation_history(self):
        """Clear the conversation history."""
        self.conversation_history = []
        self.logger.info("Conversation history cleared")

    def get_conversation_summary(self) -> Dict[str, Any]:
        """Get a summary of the current conversation."""
        return {
            "total_exchanges": len(self.conversation_history) // 2,
            "history_length": len(self.conversation_history),
            "model": self.model,
            "personality_mode": settings.PERSONALITY_MODE
        }

    def test_connection(self) -> bool:
        """
        Test the connection to OpenRouter API.

        Returns:
            True if connection is successful
        """
        try:
            test_response = self.generate_response(
                "Hello, please respond with 'Connection test successful'",
                include_history=False,
                max_tokens=50
            )
            return "successful" in test_response.lower()
        except Exception as e:
            self.logger.error(f"Connection test failed: {str(e)}")
            return False
